-- Fix function signature conflict for update_site_setting
-- This script removes all versions of the function and creates the correct one

-- Drop all possible versions of the update_site_setting function
DROP FUNCTION IF EXISTS update_site_setting CASCADE;
DROP FUNCTION IF EXISTS public.update_site_setting CASCADE;
DROP FUNCTION IF EXISTS update_site_setting(TEXT, TEXT, TEXT, TEXT, UUID);
DROP FUNCTION IF EXISTS update_site_setting(TEXT, TEXT, TEXT, TEXT, UUID, UUID);
DROP FUNCTION IF EXISTS public.update_site_setting(TEXT, TEXT, TEXT, TEXT, UUID);
DROP FUNCTION IF EXISTS public.update_site_setting(TEXT, TEXT, TEXT, TEXT, UUID, UUID);

-- Also try dropping with different parameter combinations
DROP FUNCTION IF EXISTS update_site_setting(key_name TEXT, new_value TEXT, new_cloudinary_url TEXT, new_cloudinary_public_id TEXT, user_id UUID);
DROP FUNCTION IF EXISTS update_site_setting(key_name TEXT, new_value TEXT, new_cloudinary_url TEXT, new_cloudinary_public_id TEXT, new_media_asset_id UUID, user_id UUID);

-- Create the correct function with proper parameter handling
CREATE OR REPLACE FUNCTION update_site_setting(
    key_name TEXT,
    new_value TEXT DEFAULT NULL,
    new_cloudinary_url TEXT DEFAULT NULL,
    new_cloudinary_public_id TEXT DEFAULT NULL,
    user_id UUID DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE site_settings
    SET
        setting_value = COALESCE(new_value, setting_value),
        cloudinary_url = COALESCE(new_cloudinary_url, cloudinary_url),
        cloudinary_public_id = COALESCE(new_cloudinary_public_id, cloudinary_public_id),
        updated_at = NOW(),
        updated_by = user_id
    WHERE setting_key = key_name;

    -- If no rows were updated, insert a new setting
    IF NOT FOUND THEN
        INSERT INTO site_settings (
            setting_key,
            setting_value,
            cloudinary_url,
            cloudinary_public_id,
            created_by,
            updated_by
        ) VALUES (
            key_name,
            new_value,
            new_cloudinary_url,
            new_cloudinary_public_id,
            user_id,
            user_id
        );
    END IF;

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION update_site_setting(TEXT, TEXT, TEXT, TEXT, UUID) TO authenticated;
