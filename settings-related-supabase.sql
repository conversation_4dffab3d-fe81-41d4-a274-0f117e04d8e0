-- =====================================================
-- SITE SETTINGS TABLE AND FUNCTIONS
-- =====================================================

-- Check if update_updated_at_column function exists, create if not
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create site_settings table
CREATE TABLE IF NOT EXISTS site_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type VARCHAR(50) NOT NULL DEFAULT 'string',
    cloudinary_public_id VARCHAR(500),
    cloudinary_url VARCHAR(1000),
    media_asset_id UUID, -- Remove foreign key constraint for now
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID,
    updated_by UUID
);

-- Add foreign key constraint only if media_assets table exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'media_assets') THEN
        -- Drop existing constraint if it exists
        ALTER TABLE site_settings DROP CONSTRAINT IF EXISTS site_settings_media_asset_id_fkey;
        -- Add the constraint
        ALTER TABLE site_settings ADD CONSTRAINT site_settings_media_asset_id_fkey
            FOREIGN KEY (media_asset_id) REFERENCES media_assets(id) ON DELETE SET NULL;
    END IF;
END $$;

-- Add media_asset_id column if it doesn't exist (for existing tables)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'site_settings'
        AND column_name = 'media_asset_id'
    ) THEN
        ALTER TABLE site_settings ADD COLUMN media_asset_id UUID;

        -- Add foreign key constraint only if media_assets table exists
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'media_assets') THEN
            ALTER TABLE site_settings ADD CONSTRAINT site_settings_media_asset_id_fkey
                FOREIGN KEY (media_asset_id) REFERENCES media_assets(id) ON DELETE SET NULL;
        END IF;
    END IF;
END $$;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_site_settings_key ON site_settings(setting_key);
CREATE INDEX IF NOT EXISTS idx_site_settings_type ON site_settings(setting_type);
CREATE INDEX IF NOT EXISTS idx_site_settings_active ON site_settings(is_active);
CREATE INDEX IF NOT EXISTS idx_site_settings_media_asset_id ON site_settings(media_asset_id);

-- Create updated_at trigger
DROP TRIGGER IF EXISTS update_site_settings_updated_at ON site_settings;
CREATE TRIGGER update_site_settings_updated_at
    BEFORE UPDATE ON site_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS
DO $$ BEGIN
    ALTER TABLE site_settings ENABLE ROW LEVEL SECURITY;
EXCEPTION
    WHEN OTHERS THEN NULL;
END $$;

-- Create RLS policies
DROP POLICY IF EXISTS "Allow authenticated users to read site settings" ON site_settings;
CREATE POLICY "Allow authenticated users to read site settings" ON site_settings
    FOR SELECT USING (auth.role() = 'authenticated' AND is_active = true);

DROP POLICY IF EXISTS "Allow authenticated users to update site settings" ON site_settings;
CREATE POLICY "Allow authenticated users to update site settings" ON site_settings
    FOR UPDATE USING (auth.role() = 'authenticated');

DROP POLICY IF EXISTS "Allow authenticated users to insert site settings" ON site_settings;
CREATE POLICY "Allow authenticated users to insert site settings" ON site_settings
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

DROP POLICY IF EXISTS "Allow service role full access to site settings" ON site_settings;
CREATE POLICY "Allow service role full access to site settings" ON site_settings
    FOR ALL USING (auth.role() = 'service_role');

-- Insert default site settings
INSERT INTO site_settings (setting_key, setting_value, setting_type, description) VALUES
    ('site_logo', '/images/logo.png', 'image', 'Main site logo displayed in header and sidebar'),
    ('hero_background', '/images/lgu-ipil.png', 'image', 'Background image for homepage hero section'),
    ('site_favicon', NULL, 'image', 'Browser favicon and app icon'),
    ('site_name', 'LGU Ipil', 'string', 'Site name displayed in headers'),
    ('site_tagline', 'Local Gov', 'string', 'Site tagline or subtitle'),
    ('site_description', 'Municipal Agriculture Office - Ipil | Personnel Management System', 'string', 'Site description for meta tags')
ON CONFLICT (setting_key) DO NOTHING;

-- Function to get site setting
DROP FUNCTION IF EXISTS get_site_setting(TEXT);
CREATE OR REPLACE FUNCTION get_site_setting(key_name TEXT)
RETURNS TABLE (
    setting_value TEXT,
    cloudinary_url VARCHAR(1000),
    cloudinary_public_id VARCHAR(500),
    media_asset_id UUID
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        s.setting_value,
        s.cloudinary_url,
        s.cloudinary_public_id,
        s.media_asset_id
    FROM site_settings s
    WHERE s.setting_key = key_name
    AND s.is_active = true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update site setting
CREATE OR REPLACE FUNCTION update_site_setting(
    key_name TEXT,
    new_value TEXT DEFAULT NULL,
    new_cloudinary_url TEXT DEFAULT NULL,
    new_cloudinary_public_id TEXT DEFAULT NULL,
    new_media_asset_id UUID DEFAULT NULL,
    user_id UUID DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE site_settings
    SET
        setting_value = COALESCE(new_value, setting_value),
        cloudinary_url = COALESCE(new_cloudinary_url, cloudinary_url),
        cloudinary_public_id = COALESCE(new_cloudinary_public_id, cloudinary_public_id),
        media_asset_id = COALESCE(new_media_asset_id, media_asset_id),
        updated_at = NOW(),
        updated_by = user_id
    WHERE setting_key = key_name;

    -- If no rows were updated, insert a new setting
    IF NOT FOUND THEN
        INSERT INTO site_settings (
            setting_key,
            setting_value,
            cloudinary_url,
            cloudinary_public_id,
            media_asset_id,
            created_by,
            updated_by
        ) VALUES (
            key_name,
            new_value,
            new_cloudinary_url,
            new_cloudinary_public_id,
            new_media_asset_id,
            user_id,
            user_id
        );
    END IF;

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON site_settings TO authenticated;
GRANT EXECUTE ON FUNCTION get_site_setting(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION update_site_setting(TEXT, TEXT, TEXT, TEXT, UUID, UUID) TO authenticated;