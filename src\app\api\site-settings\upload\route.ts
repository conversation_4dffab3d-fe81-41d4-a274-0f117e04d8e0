import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/utils/supabase/server'
import { uploadToCloudinary } from '@/lib/cloudinary'

export async function POST(request: NextRequest) {
  try {
    console.log('[Site Settings Upload API] Starting upload process')

    // Get the form data
    const formData = await request.formData()
    const file = formData.get('file') as File
    const key = formData.get('key') as string || formData.get('setting_key') as string
    const type = formData.get('type') as string

    if (!file || !key || !type) {
      console.error('[Site Settings Upload API] Missing required fields:', { file: !!file, key, type })
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields: file, key, and type are required'
        },
        { status: 400 }
      )
    }

    console.log('[Site Settings Upload API] Upload request:', { key, type, fileName: file.name, fileSize: file.size })

    // Upload directly to Cloudinary (avoid internal API call)
    console.log('[Site Settings Upload API] Uploading to Cloudinary...')

    let cloudinaryResult
    try {
      cloudinaryResult = await uploadToCloudinary(file, {
        folder: 'lgu-uploads/site-assets',
        tags: ['lgu-project', 'site-assets', type],
        resource_type: 'auto'
      })

      console.log('[Site Settings Upload API] Cloudinary upload successful:', {
        url: cloudinaryResult.secure_url,
        publicId: cloudinaryResult.public_id
      })
    } catch (cloudinaryError) {
      console.error('[Site Settings Upload API] Cloudinary upload failed:', cloudinaryError)
      return NextResponse.json(
        {
          success: false,
          error: `Cloudinary upload failed: ${cloudinaryError instanceof Error ? cloudinaryError.message : 'Unknown error'}`
        },
        { status: 500 }
      )
    }

    // Save to database using the update_site_setting function
    const supabase = await createClient()

    // Get current user for audit trail
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      console.error('[Site Settings Upload API] Authentication error:', authError)
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required'
        },
        { status: 401 }
      )
    }

    console.log('[Site Settings Upload API] Saving to database:', {
      key,
      value: cloudinaryResult.secure_url,
      cloudinary_url: cloudinaryResult.secure_url,
      cloudinary_public_id: cloudinaryResult.public_id,
      user_id: user.id
    })

    // Use the update_site_setting function
    const { data, error } = await supabase.rpc('update_site_setting', {
      key_name: key,
      new_value: cloudinaryResult.secure_url,
      new_cloudinary_url: cloudinaryResult.secure_url,
      new_cloudinary_public_id: cloudinaryResult.public_id,
      user_id: user.id
    })

    if (error) {
      console.error('[Site Settings Upload API] Database error:', error)
      
      // Handle missing function/table gracefully
      if (error.code === '42883' || error.code === '42P01' || error.message?.includes('does not exist')) {
        console.warn('[Site Settings Upload API] Database function/table does not exist, but upload was successful')
        return NextResponse.json({
          success: true,
          data: {
            key,
            value: cloudinaryResult.secure_url,
            cloudinary_url: cloudinaryResult.secure_url,
            cloudinary_public_id: cloudinaryResult.public_id
          },
          warning: 'File uploaded successfully but could not save to database (table does not exist)'
        })
      }

      return NextResponse.json(
        { 
          success: false, 
          error: `Database error: ${error.message}`,
          code: error.code
        },
        { status: 500 }
      )
    }

    if (!data) {
      console.error('[Site Settings Upload API] Function returned no data')
      return NextResponse.json(
        {
          success: false,
          error: 'Database function failed to execute'
        },
        { status: 500 }
      )
    }

    console.log('[Site Settings Upload API] Upload completed successfully:', data)

    return NextResponse.json({
      success: true,
      cloudinary_url: cloudinaryResult.secure_url,
      cloudinary_public_id: cloudinaryResult.public_id,
      data: {
        setting_key: key,
        setting_value: cloudinaryResult.secure_url,
        cloudinary_url: cloudinaryResult.secure_url,
        cloudinary_public_id: cloudinaryResult.public_id
      }
    })

  } catch (error) {
    console.error('[Site Settings Upload API] Unexpected error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Internal server error' 
      },
      { status: 500 }
    )
  }
}
