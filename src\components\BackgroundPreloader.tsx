/**
 * Enterprise Background Preloader
 * 
 * Aggressively preloads hero background images to eliminate FOUC.
 * This component runs immediately when the app starts and warms the cache
 * with the current hero background, ensuring instant display.
 */

'use client'

import { useEffect } from 'react'
import { heroBackgroundCache } from '@/lib/heroBackgroundCache'

interface BackgroundPreloaderProps {
  fallbackSrc?: string
}

export function BackgroundPreloader({ 
  fallbackSrc = "/images/lgu-ipil.png" 
}: BackgroundPreloaderProps) {
  
  useEffect(() => {
    console.log('[BackgroundPreloader] 🚀 Starting aggressive background preloading...')
    
    // Immediately try to fetch and cache the current hero background
    const preloadCurrentBackground = async () => {
      try {
        // Fetch current settings from API
        const response = await fetch('/api/site-settings', {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' }
        })
        
        if (response.ok) {
          const data = await response.json()
          const heroBackground = data.settings?.hero_background
          
          if (heroBackground?.cloudinary_url || heroBackground?.value) {
            const backgroundUrl = heroBackground.cloudinary_url || heroBackground.value
            
            if (backgroundUrl !== fallbackSrc) {
              console.log('[BackgroundPreloader] 🎯 Preloading hero background:', backgroundUrl)
              
              // Aggressively cache the background
              const success = await heroBackgroundCache.set('hero_background', backgroundUrl, fallbackSrc)
              
              if (success) {
                console.log('[BackgroundPreloader] ✅ Hero background cached successfully')
              } else {
                console.warn('[BackgroundPreloader] ⚠️ Failed to cache hero background')
              }
            }
          }
        }
      } catch (error) {
        console.warn('[BackgroundPreloader] ⚠️ Failed to preload background:', error)
      }
    }
    
    // Also preload the fallback image
    const preloadFallback = async () => {
      try {
        console.log('[BackgroundPreloader] 🎯 Preloading fallback background:', fallbackSrc)
        await heroBackgroundCache.set('hero_background_fallback', fallbackSrc)
      } catch (error) {
        console.warn('[BackgroundPreloader] ⚠️ Failed to preload fallback:', error)
      }
    }
    
    // Run preloading immediately
    preloadCurrentBackground()
    preloadFallback()
    
    // Set up periodic cache warming (every 5 minutes)
    const interval = setInterval(() => {
      console.log('[BackgroundPreloader] 🔄 Warming cache...')
      preloadCurrentBackground()
    }, 5 * 60 * 1000)
    
    return () => clearInterval(interval)
  }, [fallbackSrc])
  
  // This component renders nothing but does critical background work
  return null
}

/**
 * Critical Background Preloader
 * 
 * Ultra-aggressive preloader that runs before anything else
 */
export function CriticalBackgroundPreloader() {
  useEffect(() => {
    // 🔧 HYDRATION-SAFE: Only run on client-side after hydration
    if (typeof window === 'undefined') return

    // Run immediately on mount, before any other components
    const criticalPreload = () => {
      console.log('[CriticalBackgroundPreloader] ⚡ Critical preload starting...')

      // Try to get cached background immediately (now hydration-safe)
      const cached = heroBackgroundCache.get('hero_background')
      if (cached) {
        console.log('[CriticalBackgroundPreloader] ⚡ Found cached background:', cached)

        // Create invisible image to ensure it's in browser cache
        const img = new Image()
        img.src = cached
        img.onload = () => console.log('[CriticalBackgroundPreloader] ✅ Browser cache warmed')
      }
    }

    // Run immediately
    criticalPreload()

    // Also run after a short delay to catch any updates
    const timeout = setTimeout(criticalPreload, 100)

    return () => clearTimeout(timeout)
  }, [])

  return null
}
