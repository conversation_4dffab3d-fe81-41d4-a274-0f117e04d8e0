/**
 * Enterprise Hero Background Cache Service
 * 
 * Advanced caching system for hero background images that eliminates FOUC
 * by providing instant access to previously loaded backgrounds.
 * 
 * Features:
 * - LocalStorage persistence
 * - Memory cache for ultra-fast access
 * - Automatic cache invalidation
 * - Preloading strategies
 * - Error handling and fallbacks
 */

interface CachedBackground {
  url: string
  timestamp: number
  preloaded: boolean
  fallback?: string
}

interface CacheConfig {
  maxAge: number // milliseconds
  maxEntries: number
  preloadOnSet: boolean
}

class HeroBackgroundCache {
  private memoryCache = new Map<string, CachedBackground>()
  private config: CacheConfig = {
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
    maxEntries: 10,
    preloadOnSet: true
  }
  private readonly STORAGE_KEY = 'lgu_hero_backgrounds'

  constructor(config?: Partial<CacheConfig>) {
    if (config) {
      this.config = { ...this.config, ...config }
    }
    this.loadFromStorage()
  }

  /**
   * Get cached background URL (HYDRATION-SAFE)
   */
  get(key: string, fallback?: string): string | null {
    // 🔧 HYDRATION-SAFE: Only access cache on client-side
    if (typeof window === 'undefined') {
      console.log('[HeroBackgroundCache] SSR detected, returning fallback:', fallback)
      return fallback || null
    }

    // Check memory cache first (fastest)
    const memoryEntry = this.memoryCache.get(key)
    if (memoryEntry && this.isValid(memoryEntry)) {
      console.log('[HeroBackgroundCache] Memory cache hit:', key)
      return memoryEntry.url
    }

    // Check localStorage (fast)
    const storageEntry = this.getFromStorage(key)
    if (storageEntry && this.isValid(storageEntry)) {
      console.log('[HeroBackgroundCache] Storage cache hit:', key)
      // Promote to memory cache
      this.memoryCache.set(key, storageEntry)
      return storageEntry.url
    }

    console.log('[HeroBackgroundCache] Cache miss:', key)
    return fallback || null
  }

  /**
   * Set background URL in cache with preloading
   */
  async set(key: string, url: string, fallback?: string): Promise<boolean> {
    const entry: CachedBackground = {
      url,
      timestamp: Date.now(),
      preloaded: false,
      fallback
    }

    try {
      // Preload image if enabled
      if (this.config.preloadOnSet) {
        const preloaded = await this.preloadImage(url)
        entry.preloaded = preloaded
        
        if (!preloaded) {
          console.warn('[HeroBackgroundCache] Failed to preload image:', url)
          return false
        }
      }

      // Store in memory cache
      this.memoryCache.set(key, entry)

      // Store in localStorage
      this.saveToStorage(key, entry)

      console.log('[HeroBackgroundCache] Cached background:', key, url)
      return true
    } catch (error) {
      console.error('[HeroBackgroundCache] Failed to cache background:', error)
      return false
    }
  }

  /**
   * Preload image and return success status
   */
  private preloadImage(url: string): Promise<boolean> {
    return new Promise((resolve) => {
      const img = new Image()
      img.onload = () => resolve(true)
      img.onerror = () => resolve(false)
      img.src = url
    })
  }

  /**
   * Check if cache entry is still valid
   */
  private isValid(entry: CachedBackground): boolean {
    const age = Date.now() - entry.timestamp
    return age < this.config.maxAge
  }

  /**
   * Load cache from localStorage
   */
  private loadFromStorage(): void {
    if (typeof window === 'undefined') return

    try {
      const stored = localStorage.getItem(this.STORAGE_KEY)
      if (stored) {
        const data = JSON.parse(stored)
        Object.entries(data).forEach(([key, entry]) => {
          if (this.isValid(entry as CachedBackground)) {
            this.memoryCache.set(key, entry as CachedBackground)
          }
        })
      }
    } catch (error) {
      console.warn('[HeroBackgroundCache] Failed to load from storage:', error)
    }
  }

  /**
   * Get single entry from localStorage
   */
  private getFromStorage(key: string): CachedBackground | null {
    if (typeof window === 'undefined') return null

    try {
      const stored = localStorage.getItem(this.STORAGE_KEY)
      if (stored) {
        const data = JSON.parse(stored)
        return data[key] || null
      }
    } catch (error) {
      console.warn('[HeroBackgroundCache] Failed to get from storage:', error)
    }
    return null
  }

  /**
   * Save entry to localStorage
   */
  private saveToStorage(key: string, entry: CachedBackground): void {
    if (typeof window === 'undefined') return

    try {
      const stored = localStorage.getItem(this.STORAGE_KEY)
      const data = stored ? JSON.parse(stored) : {}
      
      data[key] = entry
      
      // Cleanup old entries if needed
      this.cleanupStorage(data)
      
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(data))
    } catch (error) {
      console.warn('[HeroBackgroundCache] Failed to save to storage:', error)
    }
  }

  /**
   * Cleanup old entries from storage
   */
  private cleanupStorage(data: Record<string, CachedBackground>): void {
    const entries = Object.entries(data)
    
    // Remove expired entries
    const validEntries = entries.filter(([, entry]) => this.isValid(entry))
    
    // Limit number of entries
    if (validEntries.length > this.config.maxEntries) {
      validEntries.sort(([, a], [, b]) => b.timestamp - a.timestamp)
      validEntries.splice(this.config.maxEntries)
    }
    
    // Rebuild data object
    Object.keys(data).forEach(key => delete data[key])
    validEntries.forEach(([key, entry]) => {
      data[key] = entry
    })
  }

  /**
   * Clear all cache
   */
  clear(): void {
    this.memoryCache.clear()
    if (typeof window !== 'undefined') {
      localStorage.removeItem(this.STORAGE_KEY)
    }
  }

  /**
   * Get cache statistics
   */
  getStats() {
    return {
      memoryEntries: this.memoryCache.size,
      config: this.config
    }
  }
}

// Singleton instance
export const heroBackgroundCache = new HeroBackgroundCache()

// Export for custom configurations
export { HeroBackgroundCache }
export type { CachedBackground, CacheConfig }
